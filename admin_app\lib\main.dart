import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'constants/app_theme.dart';
import 'constants/app_constants.dart';
import 'screens/login_screen.dart';
import 'screens/enhanced_dashboard_screen.dart';
import 'screens/user_management_screen.dart';
import 'screens/reseller_applications_screen.dart';
import 'screens/admin_notification_screen.dart';
import 'screens/splash_screen.dart';
import 'screens/not_found_screen.dart';
import 'providers/auth_provider.dart';
import 'services/navigation_service.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  runApp(const AmalPointAdminApp());
}

class AmalPointAdminApp extends StatelessWidget {
  const AmalPointAdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        Provider(create: (_) => NavigationService()),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.lightTheme,
        themeMode: ThemeMode.light,
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en', ''), // English
          Locale('bn', ''), // Bengali
        ],
        onGenerateRoute: (settings) {
          // Handle dynamic routes here
          return MaterialPageRoute(
            builder: (context) => const NotFoundScreen(),
          );
        },
        routes: {
          '/': (context) => const AuthWrapper(),
          '/login': (context) => const LoginScreen(),
          '/dashboard': (context) => const EnhancedDashboardScreen(),
          '/users': (context) => const UserManagementScreen(),
          '/reseller-applications': (context) => const ResellerApplicationsScreen(),
          '/posts': (context) => const EnhancedDashboardScreen(initialRoute: '/posts'),
          '/comments': (context) => const EnhancedDashboardScreen(initialRoute: '/comments'),
          '/reports': (context) => const EnhancedDashboardScreen(initialRoute: '/reports'),
          '/admin-notifications': (context) => const AdminNotificationScreen(),
        },
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        switch (authProvider.status) {
          case AuthStatus.uninitialized:
            return const SplashScreen();
          case AuthStatus.authenticated:
            return _buildAuthenticatedApp(context);
          case AuthStatus.unauthenticated:
          case AuthStatus.loading:
          default:
            return const LoginScreen();
        }
      },
    );
  }

  Widget _buildAuthenticatedApp(BuildContext context) {
    return Navigator(
      onGenerateRoute: (settings) {
        print('Navigating to route: ${settings.name}');
        WidgetBuilder builder;
        switch (settings.name) {
          case '/':
          case '/dashboard':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/users':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/resellers':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/admins':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/products':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/categories':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/posts':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/comments':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/content':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/settings':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/notifications':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/reports':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          case '/moderation':
          case '/moderation/posts':
          case '/moderation/comments':
          case '/moderation/reports':
            builder = (context) => EnhancedDashboardScreen(initialRoute: settings.name);
            break;
          default:
            builder = (context) => const NotFoundScreen();
        }

        return MaterialPageRoute(
          builder: builder,
          settings: settings,
        );
      },
    );
  }
}