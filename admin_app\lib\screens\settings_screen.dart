import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import 'user_registration_approval_screen.dart';
import 'reseller_applications_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Settings'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        children: [
          // User Management Section
          _buildSectionHeader('User Management'),
          _buildSettingCard(
            title: 'User Registration Approval',
            subtitle: 'Approve or reject new user registrations',
            icon: Icons.person_add,
            color: AppConstants.primaryColor,
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const UserRegistrationApprovalScreen(),
                ),
              );
            },
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),

          // Reseller Management Section
          _buildSectionHeader('Reseller Management'),
          _buildSettingCard(
            title: 'Reseller Applications',
            subtitle: 'Review and approve reseller applications',
            icon: Icons.business,
            color: AppConstants.warningColor,
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const ResellerApplicationsScreen(),
                ),
              );
            },
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // System Settings Section
          _buildSectionHeader('System Settings'),
          _buildSettingCard(
            title: 'App Configuration',
            subtitle: 'Configure app-wide settings and preferences',
            icon: Icons.settings,
            color: AppConstants.textSecondaryColor,
            onTap: () {
              _showAppConfigurationDialog();
            },
          ),
          
          _buildSettingCard(
            title: 'Notification Settings',
            subtitle: 'Configure push notifications and alerts',
            icon: Icons.notifications,
            color: AppConstants.infoColor,
            onTap: () {
              _showNotificationSettingsDialog();
            },
          ),

          _buildSettingCard(
            title: 'Security Settings',
            subtitle: 'Manage security policies and access controls',
            icon: Icons.security,
            color: AppConstants.errorColor,
            onTap: () {
              _showSecuritySettingsDialog();
            },
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Content Management Section
          _buildSectionHeader('Content Management'),
          _buildSettingCard(
            title: 'Content Moderation',
            subtitle: 'Configure content filtering and moderation rules',
            icon: Icons.content_paste,
            color: AppConstants.successColor,
            onTap: () {
              _showContentModerationDialog();
            },
          ),

          _buildSettingCard(
            title: 'Category Management',
            subtitle: 'Manage product and content categories',
            icon: Icons.category,
            color: AppConstants.primaryColor,
            onTap: () {
              _showCategoryManagementDialog();
            },
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Analytics Section
          _buildSectionHeader('Analytics & Reports'),
          _buildSettingCard(
            title: 'Analytics Configuration',
            subtitle: 'Configure analytics tracking and reports',
            icon: Icons.analytics,
            color: AppConstants.infoColor,
            onTap: () {
              _showAnalyticsConfigDialog();
            },
          ),

          const SizedBox(height: AppConstants.paddingLarge),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppConstants.paddingMedium,
        top: AppConstants.paddingMedium,
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppConstants.textPrimaryColor,
        ),
      ),
    );
  }

  Widget _buildSettingCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppConstants.textSecondaryColor,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppConstants.textSecondaryColor,
        ),
        onTap: onTap,
      ),
    );
  }

  void _showAppConfigurationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('App Configuration'),
        content: const Text('App configuration settings will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Settings'),
        content: const Text('Notification settings will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSecuritySettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Security Settings'),
        content: const Text('Security settings will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showContentModerationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Content Moderation'),
        content: const Text('Content moderation settings will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showCategoryManagementDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Category Management'),
        content: const Text('Category management will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAnalyticsConfigDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Analytics Configuration'),
        content: const Text('Analytics configuration will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
