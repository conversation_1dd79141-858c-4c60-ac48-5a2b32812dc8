import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../enums/user_role.dart';

enum AuthStatus {
  uninitialized,
  authenticated,
  unauthenticated,
  loading,
}

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  
  AuthStatus _status = AuthStatus.uninitialized;
  UserModel? _currentUser;
  String? _errorMessage;

  // Getters
  AuthStatus get status => _status;
  UserModel? get currentUser => _currentUser;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _status == AuthStatus.authenticated;
  bool get isLoading => _status == AuthStatus.loading;

  AuthProvider() {
    _initializeAuth();
  }

  // Initialize authentication state
  void _initializeAuth() {
    _authService.authStateChanges.listen((User? user) async {
      if (user != null) {
        try {
          final userData = await _authService.getCurrentUserData();
          if (userData != null) {
            _currentUser = userData;
            _status = AuthStatus.authenticated;
          } else {
            _status = AuthStatus.unauthenticated;
          }
        } catch (e) {
          _status = AuthStatus.unauthenticated;
          _errorMessage = e.toString();
        }
      } else {
        _currentUser = null;
        _status = AuthStatus.unauthenticated;
      }
      notifyListeners();
    });
  }

  // Sign up with email and password
  Future<bool> signUp({
    required String email,
    required String password,
    required String username,
    required String displayName,
  }) async {
    try {
      _setLoading();
      _clearError();

      final user = await _authService.signUpWithEmailAndPassword(
        email: email,
        password: password,
        username: username,
        displayName: displayName,
      );

      if (user != null) {
        _currentUser = user;
        _status = AuthStatus.authenticated;
        notifyListeners();
        return true;
      }
      
      _status = AuthStatus.unauthenticated;
      notifyListeners();
      return false;
    } catch (e) {
      _status = AuthStatus.unauthenticated;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Sign in with email and password
  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading();
      _clearError();

      final user = await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (user != null) {
        _currentUser = user;
        _status = AuthStatus.authenticated;
        notifyListeners();
        return true;
      }
      
      _status = AuthStatus.unauthenticated;
      notifyListeners();
      return false;
    } catch (e) {
      _status = AuthStatus.unauthenticated;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      _setLoading();
      await _authService.signOut();
      _currentUser = null;
      _status = AuthStatus.unauthenticated;
      _clearError();
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Reset password
  Future<bool> resetPassword({required String email}) async {
    try {
      _setLoading();
      _clearError();

      await _authService.resetPassword(email: email);
      
      _status = AuthStatus.unauthenticated;
      notifyListeners();
      return true;
    } catch (e) {
      _status = AuthStatus.unauthenticated;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update user profile
  Future<bool> updateProfile({
    String? displayName,
    String? username,
    String? bio,
    String? profileImageUrl,
    String? coverImageUrl,
    String? gender,
    String? address,
    String? country,
  }) async {
    if (_currentUser == null) return false;

    try {
      _setLoading();
      _clearError();

      await _authService.updateUserProfile(
        userId: _currentUser!.id,
        displayName: displayName,
        username: username,
        bio: bio,
        profileImageUrl: profileImageUrl,
        coverImageUrl: coverImageUrl,
        gender: gender,
        address: address,
        country: country,
      );

      // Update local user data
      _currentUser = _currentUser!.copyWith(
        displayName: displayName ?? _currentUser!.displayName,
        username: username ?? _currentUser!.username,
        bio: bio ?? _currentUser!.bio,
        profileImageUrl: profileImageUrl ?? _currentUser!.profileImageUrl,
        coverImageUrl: coverImageUrl ?? _currentUser!.coverImageUrl,
        gender: gender ?? _currentUser!.gender,
        address: address ?? _currentUser!.address,
        country: country ?? _currentUser!.country,
        updatedAt: DateTime.now(),
      );

      _status = AuthStatus.authenticated;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update profile image only
  Future<bool> updateProfileImage(String? profileImageUrl) async {
    if (_currentUser == null) return false;

    try {
      _setLoading();
      _clearError();

      await _authService.updateUserProfile(
        userId: _currentUser!.id,
        profileImageUrl: profileImageUrl,
      );

      // Update local user data
      _currentUser = _currentUser!.copyWith(
        profileImageUrl: profileImageUrl,
        updatedAt: DateTime.now(),
      );

      _status = AuthStatus.authenticated;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update cover image only
  Future<bool> updateCoverImage(String? coverImageUrl) async {
    if (_currentUser == null) return false;

    try {
      _setLoading();
      _clearError();

      await _authService.updateUserProfile(
        userId: _currentUser!.id,
        coverImageUrl: coverImageUrl,
      );

      // Update local user data
      _currentUser = _currentUser!.copyWith(
        coverImageUrl: coverImageUrl,
        updatedAt: DateTime.now(),
      );

      _status = AuthStatus.authenticated;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      _setLoading();
      _clearError();

      await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      _status = AuthStatus.authenticated;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Follow/Unfollow user
  Future<bool> toggleFollowUser({required String targetUserId}) async {
    if (_currentUser == null) return false;

    try {
      await _authService.toggleFollowUser(
        currentUserId: _currentUser!.id,
        targetUserId: targetUserId,
      );

      // Update local user data
      List<String> newFollowing = List.from(_currentUser!.following);
      if (newFollowing.contains(targetUserId)) {
        newFollowing.remove(targetUserId);
      } else {
        newFollowing.add(targetUserId);
      }

      _currentUser = _currentUser!.copyWith(
        following: newFollowing,
        updatedAt: DateTime.now(),
      );

      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Search users
  Future<List<UserModel>> searchUsers({
    required String query,
    int limit = 20,
  }) async {
    try {
      return await _authService.searchUsers(query: query, limit: limit);
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return [];
    }
  }

  // Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      return await _authService.getUserById(userId);
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    }
  }

  // Helper methods
  void _setLoading() {
    _status = AuthStatus.loading;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  void clearError() {
    _clearError();
    notifyListeners();
  }

  // Submit reseller application
  Future<bool> submitResellerApplication({required String reason}) async {
    if (_currentUser == null) return false;

    try {
      final success = await _authService.submitResellerApplication(
        userId: _currentUser!.id,
        reason: reason,
      );

      if (success) {
        // Update local user data
        _currentUser = _currentUser!.copyWith(
          resellerApplicationStatus: ResellerApplicationStatus.pending,
          resellerApplicationDate: DateTime.now(),
          resellerApplicationReason: reason,
          updatedAt: DateTime.now(),
        );
        notifyListeners();
      }

      return success;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Check authentication status (for splash screen)
  Future<void> checkAuthStatus() async {
    try {
      _setLoading();

      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userData = await _authService.getCurrentUserData();
        if (userData != null) {
          _currentUser = userData;
          _status = AuthStatus.authenticated;
        } else {
          _status = AuthStatus.unauthenticated;
        }
      } else {
        _currentUser = null;
        _status = AuthStatus.unauthenticated;
      }

      notifyListeners();
    } catch (e) {
      _status = AuthStatus.unauthenticated;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Refresh current user data from Firestore
  Future<void> refreshCurrentUser() async {
    if (_auth.currentUser == null) return;

    try {
      final userData = await _authService.getCurrentUserData();
      if (userData != null) {
        _currentUser = userData;
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }
}
